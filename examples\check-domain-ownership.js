/**
 * Check Domain Ownership
 * Quick test to verify domain ownership for test@xxx
 */

require('dotenv').config();
const ODudeSDK = require('../src/index');

async function main() {
  console.log('=== Domain Ownership Check ===\n');

  const sdk = new ODudeSDK({
    rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org'
  });

  try {
    sdk.connectNetwork('basesepolia');
    console.log('✅ Connected to Base Sepolia network');
  } catch (error) {
    console.log('❌ Failed to connect:', error.message);
    return;
  }

  const domainName = 'test@xxx';
  const expectedOwner = '0xDF9dcaDF518670560fFDD62c3675304bDE8B8015';

  console.log(`\n🔍 Checking domain: ${domainName}`);
  console.log(`🔍 Expected owner: ${expectedOwner}`);

  try {
    // Check if domain exists and get owner
    const owner = await sdk.getOwner(domainName);
    console.log(`👤 Actual owner: ${owner}`);
    
    if (owner.toLowerCase() === expectedOwner.toLowerCase()) {
      console.log('✅ Owner matches expected address');
    } else if (owner === '0x0000000000000000000000000000000000000000') {
      console.log('❌ Domain does not exist or is not minted');
    } else {
      console.log('⚠️  Domain exists but owned by different address');
    }

    // Try to resolve the domain
    const resolvedAddress = await sdk.resolve(domainName);
    console.log(`🔗 Resolved address: ${resolvedAddress}`);

    // Try reverse lookup
    const reverseName = await sdk.reverse(expectedOwner);
    console.log(`🔄 Reverse lookup for ${expectedOwner}: ${reverseName}`);

  } catch (error) {
    console.log('❌ Error checking domain:', error.message);
  }

  // Also check what domains the address actually owns
  console.log(`\n🔍 Checking all domains owned by ${expectedOwner}:`);
  try {
    const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(expectedOwner, 'xxx');
    console.log(`📝 Domains in 'xxx' TLD: ${userDomains.length}`);
    if (userDomains.length > 0) {
      userDomains.forEach((domain, index) => {
        console.log(`  ${index + 1}. ${domain}`);
      });
    } else {
      console.log('  No domains found in xxx TLD');
    }
  } catch (error) {
    console.log('❌ Error getting user domains:', error.message);
  }

  console.log('\n✓ Check completed!');
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });
